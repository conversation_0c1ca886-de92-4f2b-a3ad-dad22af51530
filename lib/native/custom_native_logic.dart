import 'package:flutter/services.dart';

class BatteryService {
  static const MethodChannel _channel = MethodChannel('com.example.rooo_driver/battery');

  /// Get the current battery percentage
  /// Returns a value between 0-100, or throws an exception if unavailable
  static Future<int> getBatteryLevel() async {
    try {
      final int batteryLevel = await _channel.invokeMethod('getBatteryLevel');
      return batteryLevel;
    } on PlatformException catch (e) {
      throw Exception('Failed to get battery level: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error getting battery level: $e');
    }
  }

  /// Get battery level as a formatted string with percentage symbol
  static Future<String> getBatteryLevelString() async {
    try {
      final int level = await getBatteryLevel();
      return '$level%';
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Check if battery information is available
  static Future<bool> isBatteryInfoAvailable() async {
    try {
      await getBatteryLevel();
      return true;
    } catch (e) {
      return false;
    }
  }
}

class CustomNativeLogic {
  // Keep the original class for any future native logic
}
